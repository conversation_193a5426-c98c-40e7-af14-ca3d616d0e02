import { colton, compton, liora, regis, scyra } from '@/assets/images';

import { AgenticTickerEntry } from '../types/agenticTicker';

// Mock agent images - these would be replaced with actual agent images
const agentImages = {
  scyra: scyra,
  obed: liora,
  colton: colton,
  regis: regis,
  compton: compton,
  liora: liora,
};

const eventTypes: ('Message' | 'Task' | 'Follow-Up')[] = [
  'Message',
  'Task',
  'Follow-Up',
];
const statuses: ('NOT STARTED' | 'IN PROGRESS' | 'COMPLETED' | 'BLOCKED')[] = [
  'NOT STARTED',
  'IN PROGRESS',
  'COMPLETED',
  'BLOCKED',
];

const agents = [
  { name: '<PERSON><PERSON>', image: agentImages.scyra },
  { name: 'Obed', image: agentImages.obed },
  { name: '<PERSON>', image: agentImages.colton },
  { name: '<PERSON>', image: agentImages.regis },
  { name: '<PERSON>', image: agentImages.compton },
  { name: '<PERSON><PERSON>', image: agent<PERSON>mages.liora },
];

const externalClients = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
];

const generateEventId = (type: string, index: number): string => {
  const prefix = type === 'Message' ? 'MSG' : type === 'Task' ? 'TS<PERSON>' : 'FUP';
  const id = (839103 + index).toString();
  return `${prefix}-${id}`;
};

const getRandomDate = (daysBack: number = 30): Date => {
  const now = new Date();
  const randomDays = Math.floor(Math.random() * daysBack);
  const randomHours = Math.floor(Math.random() * 24);
  const randomMinutes = Math.floor(Math.random() * 60);

  const date = new Date(now);
  date.setDate(date.getDate() - randomDays);
  date.setHours(randomHours, randomMinutes, 0, 0);

  return date;
};

const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

export const generateMockAgenticTickerData = (
  count: number = 50
): AgenticTickerEntry[] => {
  const entries: AgenticTickerEntry[] = [];

  for (let i = 0; i < count; i++) {
    const eventType = getRandomItem(eventTypes);
    const owner = getRandomItem(agents);
    const isExternalReceiver = Math.random() > 0.6; // 40% chance of external receiver

    let receiver;
    if (isExternalReceiver) {
      receiver = {
        name: getRandomItem(externalClients),
        isExternal: true,
      };
    } else {
      const agentReceiver = getRandomItem(
        agents.filter(a => a.name !== owner.name)
      );
      receiver = {
        name: agentReceiver.name,
        image: agentReceiver.image,
        isExternal: false,
      };
    }

    entries.push({
      id: `entry-${i + 1}`,
      eventId: generateEventId(eventType, i),
      eventType,
      owner,
      receiver,
      timestamp: getRandomDate(),
      status: getRandomItem(statuses),
    });
  }

  // Sort by timestamp (most recent first)
  return entries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
};

export const mockAgenticTickerEntries = generateMockAgenticTickerData(50);
