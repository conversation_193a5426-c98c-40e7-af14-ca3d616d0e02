import { AlertCircle, RefreshCw, Zap } from 'lucide-react';
import React from 'react';

import { silentRocket } from '@/assets/images';

import { Icons } from '../../../../assets/icons/DashboardIcons';

interface EmptyStateProps {
  type: 'no-data' | 'no-results' | 'api-error';
  onRetry?: () => void;
  className?: string;
}

const EmptyStates: React.FC<EmptyStateProps> = ({
  type,
  onRetry,
  className = '',
}) => {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-data':
        return {
          icon: <Zap className="h-16 w-16 text-primary" />,
          title: 'Mission Ready',
          description:
            'Activate your first agent tasks to bring your mission console online.',
          actionText: 'Get Started',
          showRetry: false,
        };

      case 'no-results':
        return {
          icon: <Icons.Ticker className="h-16 w-16 text-gray-400" />,
          title: 'Mission Silent',
          description: 'No agent interactions match your current filters.',
          actionText: 'Clear Filters',
          showRetry: false,
        };

      case 'api-error':
        return {
          icon: <AlertCircle className="h-16 w-16 text-red-500" />,
          title: 'Mission Signal Lost',
          description:
            "The mission console isn't responding right now. We'll re-establish connection and update you shortly.",
          actionText: 'Retry Connection',
          showRetry: true,
        };

      default:
        return {
          icon: <Icons.Ticker className="h-16 w-16 text-gray-400" />,
          title: 'No Data Available',
          description: 'There are no entries to display at this time.',
          actionText: 'Refresh',
          showRetry: true,
        };
    }
  };

  const content = getEmptyStateContent();

  const handleAction = () => {
    if (onRetry) {
      onRetry();
    }
  };

  return (
    <div
      className={`flex flex-col items-center justify-center rounded-lg border border-white px-6 py-16 text-center ${className}`}
    >
      {/* Icon */}
      <div className="mb-6 h-24 w-24 rounded-lg bg-yellowOne p-4">
        <img src={silentRocket} alt="Mission Silent" />
      </div>

      {/* Title */}
      <h3 className="mb-3 text-lg font-semibold text-white">{content.title}</h3>

      {/* Description */}
      <p className="mb-8 max-w-md text-base leading-relaxed text-white">
        {content.description}
      </p>

      {/* Action Button */}
      <button
        onClick={handleAction}
        className="flex items-center gap-2 rounded-lg bg-primary px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      >
        {content.showRetry && <RefreshCw className="h-4 w-4" />}
        {content.actionText}
      </button>

      {/* Additional context for API error */}
      {type === 'api-error' && (
        <div className="mt-6 rounded-lg bg-red-50 p-4 text-sm text-red-700">
          <p className="font-medium">Connection Status: Offline</p>
          <p className="mt-1">
            Our systems are working to restore the connection. Please try again
            in a few moments.
          </p>
        </div>
      )}

      {/* Additional context for no data */}
      {type === 'no-data' && (
        <div className="mt-8 grid max-w-2xl grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="rounded-lg border border-gray-200 p-4">
            <Icons.Message className="mb-2 h-6 w-6 text-primary" />
            <h4 className="mb-1 font-medium text-gray-900">Messages</h4>
            <p className="text-sm text-gray-600">
              Agent communications and updates
            </p>
          </div>
          <div className="rounded-lg border border-gray-200 p-4">
            <Icons.Task className="mb-2 h-6 w-6 text-primary" />
            <h4 className="mb-1 font-medium text-gray-900">Tasks</h4>
            <p className="text-sm text-gray-600">Assigned agent activities</p>
          </div>
          <div className="rounded-lg border border-gray-200 p-4">
            <Icons.FollowUp className="mb-2 h-6 w-6 text-primary" />
            <h4 className="mb-1 font-medium text-gray-900">Follow-ups</h4>
            <p className="text-sm text-gray-600">Scheduled agent check-ins</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmptyStates;
