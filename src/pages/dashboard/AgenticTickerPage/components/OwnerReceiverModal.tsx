import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { marketplaceAgents as mockAgents } from '@/data/constants';

interface OwnerReceiverModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'owner' | 'receiver';
  data: {
    name: string;
    image?: string;
    role?: string;
    isExternal?: boolean;
    suiteName?: string;
    suiteFunction?: string;
  };
}

const OwnerReceiverModal: React.FC<OwnerReceiverModalProps> = ({
  isOpen,
  onClose,
  type,
  data,
}) => {
  if (!isOpen) return null;

  const getDisplayImage = () => {
    if (data.isExternal) {
      // For external clients, show abbreviated initials
      const initials = data.name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);

      return (
        <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-[#FFE0D1] bg-[#FFF1EB] text-xl font-bold text-grayTen">
          {initials}
        </div>
      );
    }

    return (
      <img
        src={data.image}
        alt={data.name}
        className="h-12 w-12 rounded-full border-8 border-[#FFE0D1] bg-[#FFF1EB] object-cover"
        onError={e => {
          const target = e.target as HTMLImageElement;
          target.src =
            mockAgents.find(
              agent => agent.id.toLowerCase() === data.name.toLowerCase()
            )?.image || mockAgents[0].image;
        }}
      />
    );
  };

  const getModalTitle = () => {
    return type === 'owner' ? 'Owner Information' : 'Receiver Information';
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-md rounded-2xl bg-white p-6 shadow-xl"
      >
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Icons.TickerOwner />
            <h2 className="text-lg font-semibold text-gray-900">
              {getModalTitle()}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="flex items-center justify-center rounded-full p-3 text-gray-600 hover:bg-[#FFF1EB] hover:text-blackOne"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-4">
          {/* Profile Section */}
          <div className="flex items-center gap-4 rounded-lg border border-grayTwentyEight px-4 py-3 text-sm">
            <div className="rounded-full bg-[#FFF1EB] p-1.5">
              {getDisplayImage()}
            </div>
            <div>
              <div className="font-medium text-subText">
                {data.isExternal ? 'Name' : 'Agent Name'}
              </div>
              <p className="mt-1 text-grayTen">{data.name}</p>
            </div>
          </div>

          {!data.isExternal && (
            <div className="flex items-center gap-4 rounded-lg border border-grayTwentyEight px-4 py-3 text-sm">
              <div className="rounded-full bg-[#FFF1EB] p-1.5">
                <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-[#FFE0D1] bg-[#FFF1EB] text-xl font-bold text-grayTen">
                  <Icons.Role />
                </div>
              </div>
              <div>
                <div className="font-medium text-subText">Role</div>
                <p className="mt-1 text-grayTen">{data.role}</p>
              </div>
            </div>
          )}

          {!data.isExternal && (
            <div className="flex items-center gap-4 rounded-lg border border-grayTwentyEight px-4 py-3 text-sm">
              <div className="rounded-full bg-[#FFF1EB] p-1.5">
                {getDisplayImage()}
              </div>
              <div>
                <div className="font-medium text-subText">Suite Name</div>
                <p className="mt-1 text-grayTen">{data.name}</p>
              </div>
            </div>
          )}

          {!data.isExternal && (
            <div className="flex items-center gap-4 rounded-lg border border-grayTwentyEight px-4 py-3 text-sm">
              <div className="rounded-full bg-[#FFF1EB] p-1.5">
                <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-[#FFE0D1] bg-[#FFF1EB] text-xl font-bold text-grayTen">
                  <Icons.Function />
                </div>
              </div>
              <div>
                <div className="font-medium text-subText">Suite Function</div>
                <p className="mt-1 text-grayTen">{data.name}</p>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default OwnerReceiverModal;
