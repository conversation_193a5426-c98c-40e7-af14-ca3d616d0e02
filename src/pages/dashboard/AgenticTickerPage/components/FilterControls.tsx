import { ChevronDown, ChevronUp, RotateCw } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import DateRangePicker from '@/components/ui/DateRangePicker';
import { useTenant } from '@/context/TenantContext';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';

import AgentsDropdown from '../../../../components/ui/AgentsDropdown';
import { FilterState } from '../../../../types/agenticTicker';
// import DateRangePicker from './DateRangePicker';
import MultiSelectDropdown from './MultiSelectDropdown';

interface FilterControlsProps {
  filters: FilterState;
  onFiltersChange: (filters: Partial<FilterState>) => void;
  onReload: () => void;
  isLoading?: boolean;
  className?: string;
}

const FilterControls: React.FC<FilterControlsProps> = ({
  filters,
  onFiltersChange,
  onReload,
  isLoading = false,
  className = '',
}) => {
  // const navigate = useNavigate(); // Unused for now
  const [currentSearchParams] = useSearchParams();
  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const [currentDateRangeType, setCurrentDateRangeType] = useState<string>('');

  // Refs for click outside handling
  const suitesRef = useRef<HTMLDivElement>(null);
  const agentsRef = useRef<HTMLDivElement>(null);
  const dateRef = useRef<HTMLDivElement>(null);
  const eventTypeRef = useRef<HTMLDivElement>(null);
  const statusRef = useRef<HTMLDivElement>(null);

  // Click outside handlers
  useOnClickOutside(suitesRef, () => {
    if (openDropdown === 'suites') setOpenDropdown(null);
  });
  useOnClickOutside(agentsRef, () => {
    if (openDropdown === 'agents') setOpenDropdown(null);
  });
  useOnClickOutside(dateRef, () => {
    if (openDropdown === 'date') setOpenDropdown(null);
  });
  useOnClickOutside(eventTypeRef, () => {
    if (openDropdown === 'eventType') setOpenDropdown(null);
  });
  useOnClickOutside(statusRef, () => {
    if (openDropdown === 'status') setOpenDropdown(null);
  });

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { activeAgent, setActiveAgent } = useTenant();

  const claimedAgentSuites = useMemo(
    () => userData?.userInfo?.tenant?.claimedAgentSuites ?? [],
    [userData]
  );

  const suiteOptions = useMemo(
    () =>
      claimedAgentSuites.map(suite => ({
        id: suite?.suite?.agentSuiteKey ?? '',
        name: suite?.suite?.agentSuiteName ?? '',
        icon: suite?.suite?.avatar ?? '',
        fileData: suite?.suite?.fileData ?? null,
      })),
    [claimedAgentSuites]
  );

  // Get agents only from the currently selected suite
  const currentSuiteAgents = useMemo(
    () =>
      claimedAgentSuites
        .find(suite => suite?.suite?.agentSuiteKey === selectedSuite)
        ?.suite?.availableAgents?.slice()
        .sort((a, b) =>
          (a.agentName || '').localeCompare(b.agentName || '', undefined, {
            sensitivity: 'base',
          })
        )
        .map(agent => ({
          ...agent,
          suiteKey: selectedSuite,
        })) ?? [],
    [claimedAgentSuites, selectedSuite]
  );

  // Initialize suite selection
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      // If there's an active agent, find the suite it belongs to
      if (activeAgent) {
        const suiteWithActiveAgent = claimedAgentSuites.find(suite =>
          suite?.suite?.availableAgents?.some(
            agent => agent.agentKey === activeAgent
          )
        );

        if (suiteWithActiveAgent) {
          setSelectedSuite(suiteWithActiveAgent.suite.agentSuiteKey);
        } else {
          // Fallback to first suite if active agent not found
          setSelectedSuite(suiteOptions[0].id);
        }
      } else {
        // No active agent, default to first suite
        setSelectedSuite(suiteOptions[0].id);
      }
    }
  }, [suiteOptions, selectedSuite, activeAgent, claimedAgentSuites]);

  // Ensure an agent is always selected when suite changes or agents are available
  useEffect(() => {
    if (currentSuiteAgents.length > 0) {
      // Check if current selected agent exists in the current suite
      const agentExistsInSuite = currentSuiteAgents.some(
        agent => agent.agentKey === selectedAgent
      );

      // If no agent is selected or the selected agent doesn't exist in current suite
      if (!selectedAgent || !agentExistsInSuite) {
        // Prefer activeAgent if it exists in current suite, otherwise use first agent
        const agentToSelect =
          currentSuiteAgents.find(agent => agent.agentKey === activeAgent)
            ?.agentKey || currentSuiteAgents[0].agentKey;
        setSelectedAgent(agentToSelect);
        setActiveAgent(agentToSelect);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSuiteAgents, selectedSuite]);

  const agentOptions = currentSuiteAgents.map(a => ({
    id: a.agentKey,
    name: a.agentName,
    icon: a.avatar,
  }));

  const eventTypeOptions = [
    { value: 'Message', label: 'Message', icon: 'Message' },
    { value: 'Task', label: 'Task', icon: 'Task' },
    { value: 'Follow-Up', label: 'Follow-Up', icon: 'Follow-Up' },
  ];

  const statusOptions = [
    { value: 'NOT STARTED', label: 'Not Started' },
    { value: 'IN PROGRESS', label: 'In Progress' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'BLOCKED', label: 'Blocked' },
  ];

  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    handleDropdownToggle('agents');
  };

  const handleDropdownToggle = (dropdown: string) => {
    setOpenDropdown(openDropdown === dropdown ? null : dropdown);
  };

  const handleEventTypeToggle = (eventType: string) => {
    const eventTypeValue = eventType as 'Message' | 'Task' | 'Follow-Up';
    const newEventTypes = filters.eventTypes.includes(eventTypeValue)
      ? filters.eventTypes.filter(type => type !== eventType)
      : [...filters.eventTypes, eventTypeValue];

    // Check if all options are now selected, if so, reset to empty
    if (newEventTypes.length === eventTypeOptions.length) {
      onFiltersChange({ eventTypes: [] });
    } else {
      onFiltersChange({ eventTypes: newEventTypes });
    }
  };

  const handleStatusToggle = (status: string) => {
    const statusValue = status as
      | 'NOT STARTED'
      | 'IN PROGRESS'
      | 'COMPLETED'
      | 'BLOCKED';
    const newStatuses = filters.statuses.includes(statusValue)
      ? filters.statuses.filter(s => s !== status)
      : [...filters.statuses, statusValue];

    // Check if all options are now selected, if so, reset to empty
    if (newStatuses.length === statusOptions.length) {
      onFiltersChange({ statuses: [] });
    } else {
      onFiltersChange({ statuses: newStatuses });
    }
  };

  // DateRangePicker helper functions
  const getCurrentDateRange = () => {
    return {
      from: filters.dateRange.startDate || undefined,
      to: filters.dateRange.endDate || undefined,
    };
  };

  const handleDateRangeApply = (range: { from?: Date; to?: Date }) => {
    onFiltersChange({
      dateRange: {
        ...filters.dateRange,
        startDate: range.from || null,
        endDate: range.to || null,
        quickSelect: 'Custom', // Set to custom when manually selecting dates
      },
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filter Controls */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Suites Dropdown */}
        <div ref={suitesRef} className="relative">
          <AgentsDropdown
            isOpen={openDropdown === 'suites'}
            onToggle={() => handleDropdownToggle('suites')}
            currentItem={suiteOptions.find(s => s.id === selectedSuite)}
            options={suiteOptions}
            onItemSelect={suite => {
              setSelectedSuite(suite.id);
              handleDropdownToggle('suites');
            }}
            placeholder="Select Suite"
            noOptionsMessage="No suites"
            className="min-w-32"
          />
        </div>

        {/* Agents Dropdown */}
        <div ref={agentsRef} className="relative">
          <AgentsDropdown
            isOpen={openDropdown === 'agents'}
            onToggle={() => handleDropdownToggle('agents')}
            currentItem={currentSuiteAgents
              .map(a => ({
                id: a.agentKey,
                name: a.agentName,
                icon: a.avatar,
              }))
              .find(a => a.id === selectedAgent)}
            options={agentOptions}
            onItemSelect={agent => handleAgentChange(agent.id)}
            placeholder="Select Agent"
            noOptionsMessage="No agents"
            className="min-w-32"
          />
        </div>

        {/* Date Range Picker */}
        <div ref={dateRef} className="relative">
          <button
            onClick={() => {
              setCurrentDateRangeType('date');
              setShowDateRangePicker(true);
            }}
            className="flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white px-3 py-2 text-sm font-normal text-blackOne hover:border-gray-300 focus:outline-none"
          >
            <div className="flex items-center gap-2">
              <Icons.DataDate className="h-4 w-4 sm:h-5 sm:w-5" />
              <span>Date</span>
            </div>
            {showDateRangePicker ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>

          <DateRangePicker
            key={`${currentSearchParams.get('from')}-${currentSearchParams.get('to')}-${currentDateRangeType}`}
            isOpen={showDateRangePicker}
            onClose={() => setShowDateRangePicker(false)}
            onApply={handleDateRangeApply}
            initialRange={getCurrentDateRange()}
            anchorRef={suitesRef}
          />
        </div>

        {/* Event Type Dropdown */}
        <div ref={eventTypeRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'eventType'}
            onToggle={() => handleDropdownToggle('eventType')}
            selectedItems={filters.eventTypes}
            options={eventTypeOptions}
            onItemToggle={handleEventTypeToggle}
            placeholder="Event Type"
            title="Event Types"
            icon={
              <Icons.OutlineTransaction className="h-4 w-4 sm:h-5 sm:w-5" />
            }
            className=""
          />
        </div>

        {/* Status Dropdown */}
        <div ref={statusRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'status'}
            onToggle={() => handleDropdownToggle('status')}
            selectedItems={filters.statuses}
            options={statusOptions}
            onItemToggle={handleStatusToggle}
            placeholder="Status"
            title="Status"
            icon={<Icons.StatusFilled className="h-4 w-4 sm:h-5 sm:w-5" />}
            className=""
          />
        </div>

        {/* Reload Button */}
        <button
          onClick={onReload}
          disabled={isLoading}
          className="z-10 flex h-[48px] w-[48px] items-center justify-center rounded-[10px] border border-gray-200 bg-[#23BD33] text-gray-500 hover:border-gray-300 hover:text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50"
          title="Reload feed"
        >
          <RotateCw
            className={`h-4 w-4 text-grayTen ${isLoading ? 'animate-spin' : ''}`}
          />
        </button>
      </div>
    </div>
  );
};

export default FilterControls;
