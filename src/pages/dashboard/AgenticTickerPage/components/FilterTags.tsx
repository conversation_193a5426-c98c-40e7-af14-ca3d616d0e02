import { X } from 'lucide-react';
import React from 'react';

import { FilterTag } from '../../../../types/agenticTicker';

interface FilterTagsProps {
  tags: FilterTag[];
  onRemoveTag: (tagId: string) => void;
  onClearAll: () => void;
  className?: string;
}

const FilterTags: React.FC<FilterTagsProps> = ({
  tags,
  onRemoveTag,
  onClearAll,
  className = '',
}) => {
  if (tags.length === 0) return null;

  return (
    <div className={`z-20 flex flex-wrap items-center gap-2 ${className}`}>
      {tags.map(tag => (
        <div
          key={tag.id}
          className="border-grayTen/20 flex items-center gap-1 rounded-md border px-3 py-1 text-sm"
        >
          <span className="font-medium text-subText">{tag.label}</span>
          <button
            onClick={() => onRemoveTag(tag.id)}
            className="flex items-center justify-center rounded-full p-0.5 text-subText hover:bg-primary/20"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      ))}

      {tags.length > 1 && (
        <button
          onClick={onClearAll}
          className="text-sm text-gray-500 underline hover:text-gray-700"
        >
          Clear all
        </button>
      )}
    </div>
  );
};

export default FilterTags;
