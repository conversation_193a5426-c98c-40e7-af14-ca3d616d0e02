import clsx from 'clsx';
import React from 'react';

interface StatusPillProps {
  status: 'NOT STARTED' | 'IN PROGRESS' | 'COMPLETED' | 'BLOCKED';
  className?: string;
}

const StatusPill: React.FC<StatusPillProps> = ({ status, className }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'NOT STARTED':
        return 'bg-[#8890A1] text-white';
      case 'IN PROGRESS':
        return 'bg-[#FBA320] text-white';
      case 'COMPLETED':
        return 'bg-[#3E8E58] text-white';
      case 'BLOCKED':
        return 'bg-[#CE1111] text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <span
      className={clsx(
        'inline-flex items-center rounded px-3 py-1 text-xs font-medium',
        getStatusStyles(),
        className
      )}
    >
      {status}
    </span>
  );
};

export default StatusPill;
