import React from 'react';

import { useTimezone } from '@/context/TimezoneContext';
import { agentSuites as mockAgents } from '@/data/constants';

import { Icons } from '../../../../assets/icons/DashboardIcons';
import { AgenticTickerEntry } from '../../../../types/agenticTicker';
import EmptyStates from './EmptyStates';
import StatusPill from './StatusPill';

interface AgenticTickerTableProps {
  entries: AgenticTickerEntry[];
  isLoading?: boolean;
  onOwnerClick: (owner: { name: string; image: string }) => void;
  onReceiverClick: (receiver: {
    name: string;
    image?: string;
    isExternal: boolean;
  }) => void;
  emptyStateType?: 'no-data' | 'no-results' | 'api-error';
  onRetry?: () => void;
  className?: string;
}

const AgenticTickerTable: React.FC<AgenticTickerTableProps> = ({
  entries,
  isLoading = false,
  onOwnerClick,
  onReceiverClick,
  emptyStateType = 'no-results',
  onRetry,
  className = '',
}) => {
  const { formatUserTimestamp } = useTimezone();

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'Message':
        return <Icons.Message className="h-4 w-4 text-white" />;
      case 'Task':
        return <Icons.Task className="h-4 w-4 text-white" />;
      case 'Follow-Up':
        return <Icons.FollowUp className="h-4 w-4 text-white" />;
      default:
        return null;
    }
  };

  const getReceiverDisplay = (receiver: AgenticTickerEntry['receiver']) => {
    if (receiver.isExternal) {
      // For external clients, show abbreviated initials as icon
      const initials = receiver.name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);

      return (
        <div className="flex items-center gap-2">
          <div className="flex h-8 min-w-8 items-center justify-center rounded-full border border-primary text-xs font-bold text-primary">
            {initials}
          </div>
          <span className="text-sm font-medium">{receiver.name}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        <div className="h-8 min-w-8 rounded-lg bg-[#FFE0D1]">
          <img
            src={receiver.image || '/placeholder-agent.png'}
            alt={receiver.name}
            className="h-8 w-8 rounded-lg object-cover"
            onError={e => {
              const fallbackAgent = mockAgents.find(
                agent => agent.id.toLowerCase() === receiver.name.toLowerCase()
              );
              if (fallbackAgent) {
                (e.target as HTMLImageElement).src = fallbackAgent.image;
              }
            }}
          />
        </div>
        <span className="text-sm font-medium">{receiver.name}</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className={`rounded-xl shadow-sm ${className}`}>
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="mt-4 text-sm text-white">Loading feed...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full overflow-hidden">
      <div className={`overflow-hidden text-blackOne shadow-sm ${className}`}>
        {/* Table Header */}
        <div className="relative z-10 border-y border-grayNine bg-[#FBEFE7]">
          <div className="grid grid-cols-6 gap-4 px-6 py-4 font-medium">
            <div>Owner</div>
            <div>Event Type</div>
            <div>Event ID</div>
            <div className="-ml-6">{`
              Timestamp 
              (${formatUserTimestamp(Date(), 'time').split(' ')[2]})`}</div>
            <div className="ml-4">Receiver</div>
            <div className="ml-6">Status</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-white backdrop-blur-sm">
          {entries.length === 0 ? (
            <EmptyStates
              type={emptyStateType}
              onRetry={onRetry}
              className="py-12"
            />
          ) : (
            <div className="divide-y divide-grayFifteen border-b border-grayFifteen">
              {entries.map((entry, index) => (
                <div
                  key={entry.id}
                  className={`hover:bg-white/60 grid grid-cols-6 gap-4 border-grayFifteen px-4 py-2 transition-colors ${
                    index % 2 === 0 ? 'bg-[#FBEFE7]' : ''
                  }`}
                >
                  {/* Owner */}
                  <div className="flex items-center">
                    <button
                      onClick={() => onOwnerClick(entry.owner)}
                      className="flex items-center gap-2 text-left transition-colors hover:text-primary"
                    >
                      <div className="h-8 min-w-8 rounded-lg bg-[#FFE0D1]">
                        <img
                          src={entry.owner.image}
                          alt={entry.owner.name}
                          className="h-8 w-8 rounded-lg object-cover"
                          onError={e => {
                            const fallbackAgent = mockAgents.find(
                              agent =>
                                agent.id.toLowerCase() ===
                                entry.owner.name.toLowerCase()
                            );
                            if (fallbackAgent) {
                              (e.target as HTMLImageElement).src =
                                fallbackAgent.image;
                            }
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium">
                        {entry.owner.name}
                      </span>
                    </button>
                  </div>

                  {/* Event Type */}
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center">
                      {getEventTypeIcon(entry.eventType)}
                    </div>
                    <span className="text-sm">{entry.eventType}</span>
                  </div>

                  {/* Event ID */}
                  <div className="flex items-center">
                    <span className="text-sm">{entry.eventId}</span>
                  </div>

                  {/* Timestamp */}
                  <div className="-ml-6 flex items-center gap-2 text-sm">
                    {formatUserTimestamp(entry.timestamp, 'date')}
                    <span className="rounded border border-[#FFE0D1] bg-[#FFF1EB] px-1.5 py-1">
                      {formatUserTimestamp(entry.timestamp, 'time').slice(
                        0,
                        -4
                      )}
                    </span>
                  </div>

                  {/* Receiver */}
                  <div className="ml-4 flex items-center">
                    <button
                      onClick={() => onReceiverClick(entry.receiver)}
                      className="flex items-center gap-2 text-left transition-colors hover:text-primary"
                    >
                      {getReceiverDisplay(entry.receiver)}
                    </button>
                  </div>

                  {/* Status */}
                  <div className="ml-6 flex items-center">
                    <StatusPill status={entry.status} />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgenticTickerTable;
