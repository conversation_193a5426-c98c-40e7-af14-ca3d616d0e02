import { AnimatePresence, motion } from 'framer-motion';
import { Search } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';

import { Icons } from '../../../assets/icons/DashboardIcons';
import AppContainer from '../../../components/common/AppContainer';
import Pagination from '../../../components/common/Pagination';
import { mockAgenticTickerEntries } from '../../../data/mockAgenticTickerData';
import {
  AgenticTickerEntry,
  FilterState,
  FilterTag,
} from '../../../types/agenticTicker';
import AgenticTickerTable from './components/AgenticTickerTable';
import FilterControls from './components/FilterControls';
import FilterTags from './components/FilterTags';
import OwnerReceiverModal from './components/OwnerReceiverModal';

const AgenticTickerPage: React.FC = () => {
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const filterButtonRef = useRef<HTMLDivElement>(null);

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    suites: [],
    agents: [],
    dateRange: {
      startDate: null,
      endDate: null,
      quickSelect: null,
    },
    eventTypes: [],
    statuses: [],
    searchQuery: '',
  });

  const [showFilterOptions, setShowFilterOptions] = useState(false);
  const [filterTags, setFilterTags] = useState<FilterTag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [entries, setEntries] = useState<AgenticTickerEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<AgenticTickerEntry[]>(
    []
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const [paginatedEntries, setPaginatedEntries] = useState<
    AgenticTickerEntry[]
  >([]);

  // Modal state
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    type: 'owner' | 'receiver';
    data: {
      name: string;
      image?: string;
      role?: string;
      suiteName?: string;
      suiteFunction?: string;
      isExternal?: boolean;
    };
  }>({
    isOpen: false,
    type: 'owner',
    data: { name: '', image: '', isExternal: false },
  });

  // Initialize with mock data
  useEffect(() => {
    setEntries(mockAgenticTickerEntries);
    setFilteredEntries(mockAgenticTickerEntries);
  }, []);

  // Filter entries based on active filters
  useEffect(() => {
    let filtered = [...entries];

    // Filter by search query (Event ID)
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.eventId.toLowerCase().includes(query)
      );
    }

    // Filter by event types
    if (filters.eventTypes.length > 0) {
      filtered = filtered.filter(entry =>
        filters.eventTypes.includes(entry.eventType)
      );
    }

    // Filter by statuses
    if (filters.statuses.length > 0) {
      filtered = filtered.filter(entry =>
        filters.statuses.includes(entry.status)
      );
    }

    // Filter by date range
    if (filters.dateRange.startDate || filters.dateRange.endDate) {
      filtered = filtered.filter(entry => {
        const entryDate = entry.timestamp;
        const startDate = filters.dateRange.startDate;
        const endDate = filters.dateRange.endDate;

        if (startDate && entryDate < startDate) return false;
        if (endDate && entryDate > endDate) return false;
        return true;
      });
    }

    // TODO: Add filtering by suites and agents when we have that data in entries

    setFilteredEntries(filtered);
  }, [entries, filters]);

  // Update paginated entries when filtered entries or current page changes
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedEntries(filteredEntries.slice(startIndex, endIndex));
  }, [filteredEntries, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredEntries.length / itemsPerPage);

  // Generate filter tags based on active filters
  useEffect(() => {
    const tags: FilterTag[] = [];

    // Suite tags
    filters.suites.forEach(suiteId => {
      tags.push({
        id: `suite-${suiteId}`,
        type: 'suite',
        label: `Suite: ${suiteId}`, // This would be replaced with actual suite name
        value: suiteId,
      });
    });

    // Agent tags
    filters.agents.forEach(agentId => {
      tags.push({
        id: `agent-${agentId}`,
        type: 'agent',
        label: `Agent: ${agentId}`, // This would be replaced with actual agent name
        value: agentId,
      });
    });

    // Date range tag
    if (
      filters.dateRange.startDate ||
      filters.dateRange.endDate ||
      filters.dateRange.quickSelect
    ) {
      let dateLabel = 'Date: ';
      if (
        filters.dateRange.quickSelect &&
        filters.dateRange.quickSelect !== 'Custom'
      ) {
        dateLabel += filters.dateRange.quickSelect;
      } else if (filters.dateRange.startDate && filters.dateRange.endDate) {
        dateLabel += `${filters.dateRange.startDate.toLocaleDateString()} - ${filters.dateRange.endDate.toLocaleDateString()}`;
      } else if (filters.dateRange.startDate) {
        dateLabel += `From ${filters.dateRange.startDate.toLocaleDateString()}`;
      } else if (filters.dateRange.endDate) {
        dateLabel += `Until ${filters.dateRange.endDate.toLocaleDateString()}`;
      }

      tags.push({
        id: 'date-range',
        type: 'date',
        label: dateLabel,
        value: 'date-range',
      });
    }

    // Event type tags
    filters.eventTypes.forEach(eventType => {
      tags.push({
        id: `eventType-${eventType}`,
        type: 'eventType',
        label: `Type: ${eventType}`,
        value: eventType,
      });
    });

    // Status tags
    filters.statuses.forEach(status => {
      tags.push({
        id: `status-${status}`,
        type: 'status',
        label: `Status: ${status}`,
        value: status,
      });
    });

    // Search query tag
    if (filters.searchQuery.trim()) {
      tags.push({
        id: 'search',
        type: 'eventType', // Using eventType as a generic type
        label: `Search: "${filters.searchQuery}"`,
        value: filters.searchQuery,
      });
    }

    setFilterTags(tags);
  }, [filters]);

  const handleFiltersChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    // Reset to first page when filters change
    setCurrentPage(1);
    // TODO: Update filter tags based on new filters
  };

  const handleReload = () => {
    setIsLoading(true);
    setError(null);
    // TODO: Implement reload functionality
    setTimeout(() => {
      setIsLoading(false);
      // Simulate occasional API errors for demonstration
      if (Math.random() < 0.1) {
        setError('Failed to fetch data');
      }
    }, 1000);
  };

  const handleRemoveTag = (tagId: string) => {
    const tag = filterTags.find(t => t.id === tagId);
    if (!tag) return;

    const newFilters = { ...filters };

    switch (tag.type) {
      case 'suite':
        newFilters.suites = filters.suites.filter(id => id !== tag.value);
        break;
      case 'agent':
        newFilters.agents = filters.agents.filter(id => id !== tag.value);
        break;
      case 'date':
        newFilters.dateRange = {
          startDate: null,
          endDate: null,
          quickSelect: null,
        };
        break;
      case 'eventType':
        if (tagId === 'search') {
          newFilters.searchQuery = '';
        } else {
          newFilters.eventTypes = filters.eventTypes.filter(
            type => type !== tag.value
          );
        }
        break;
      case 'status':
        newFilters.statuses = filters.statuses.filter(
          status => status !== tag.value
        );
        break;
    }

    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleClearAllTags = () => {
    setFilterTags([]);
    setFilters({
      suites: [],
      agents: [],
      dateRange: {
        startDate: null,
        endDate: null,
        quickSelect: null,
      },
      eventTypes: [],
      statuses: [],
      searchQuery: '',
    });
    // Reset to first page when clearing filters
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleOwnerClick = (owner: { name: string; image: string }) => {
    setModalState({
      isOpen: true,
      type: 'owner',
      data: {
        name: owner.name,
        image: owner.image,
        role: 'Agent Role', // TODO: Get role from entry
        suiteName: 'Suite Name', // TODO: Get suite name from entry
        suiteFunction: 'Suite Function', // TODO: Get suite function from entry
        isExternal: false,
      },
    });
  };

  const handleReceiverClick = (receiver: {
    name: string;
    image?: string;
    isExternal: boolean;
  }) => {
    setModalState({
      isOpen: true,
      type: 'receiver',
      data: {
        name: receiver.name,
        image: receiver.image,
        isExternal: receiver.isExternal,
        role: 'Agent Role', // TODO: Get role from entry
        suiteName: 'Suite Name', // TODO: Get suite name from entry
        suiteFunction: 'Suite Function', // TODO: Get suite function from entry
      },
    });
  };

  const handleCloseModal = () => {
    setModalState(prev => ({ ...prev, isOpen: false }));
  };

  // Determine empty state type
  const getEmptyStateType = (): 'no-data' | 'no-results' | 'api-error' => {
    if (error) return 'api-error';
    if (entries.length === 0) return 'no-data';
    return 'no-results';
  };

  const handleRetry = () => {
    handleReload();
  };

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <AppContainer
        isPadding={false}
        className="space-y-6 p-0 sm:p-8 lg:space-y-8"
      >
        {/* Header */}
        <div className="mb-6">
          <div className="flex w-full items-center justify-between gap-4">
            {/* Title */}
            <div className="flex flex-shrink-0 items-center gap-3">
              <Icons.Ticker className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold text-blackFour">
                Agentic Ticker (Live Feed)
              </h1>
            </div>

            {/* Search Bar and Filter Button */}
            <div className="flex items-center gap-4">
              <div className="relative h-11 min-w-0 max-w-2xl flex-1">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by Event ID"
                  value={filters.searchQuery}
                  onChange={e =>
                    handleFiltersChange({ searchQuery: e.target.value })
                  }
                  className="w-full rounded-lg border border-gray-200 py-3 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>

              <div ref={filterButtonRef}>
                <button
                  onClick={() => setShowFilterOptions(!showFilterOptions)}
                  className="flex h-[44px] items-center gap-3 rounded-[10px] border-2 border-primary bg-[#FDF7F6] px-4 text-primary transition-colors hover:bg-primary hover:text-white"
                >
                  <span className="font-spartan text-sm font-semibold">
                    Filter
                  </span>
                  <Icons.DownloadCloud className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <AnimatePresence>
          {showFilterOptions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="w-full"
            >
              <div className="px-4">
                <FilterControls
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onReload={handleReload}
                  isLoading={isLoading}
                  className=""
                />

                {/* Filter Tags */}
                <FilterTags
                  tags={filterTags}
                  onRemoveTag={handleRemoveTag}
                  onClearAll={handleClearAllTags}
                  className="mt-4"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agentic Ticker Table */}
        <AgenticTickerTable
          entries={paginatedEntries}
          isLoading={isLoading}
          onOwnerClick={handleOwnerClick}
          onReceiverClick={handleReceiverClick}
          emptyStateType={getEmptyStateType()}
          onRetry={handleRetry}
          className=""
        />

        {/* Pagination */}
        <div className="mt-6">
          {!isLoading && totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              className="mt-6"
            />
          )}
        </div>
      </AppContainer>
      {/* Owner/Receiver Modal */}
      <OwnerReceiverModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        type={modalState.type}
        data={modalState.data}
      />
    </DashboardWithChatLayout>
  );
};

export default AgenticTickerPage;
