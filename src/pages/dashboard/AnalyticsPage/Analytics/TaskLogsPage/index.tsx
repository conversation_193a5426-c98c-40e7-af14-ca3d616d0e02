import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import Pagination from '@/components/common/Pagination';
import { TablePageContainer } from '@/components/layout/DashboardWithChatLayout';
import EmptyState from '@/components/ui/EmptyState';
import DataTable, { Column } from '@/components/ui/tables/DataTable';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';
import { useDebounce } from '@/hooks/useDebounce';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { usePagination } from '@/hooks/usePagination';
import { useTaskLogsList } from '@/hooks/useTaskLog';
import {
  mapPriorityToDisplay,
  mapStatusToDisplay,
  TaskLog,
  TaskLogPriority,
  TaskLogPriorityDisplay,
  TaskLogStatus,
  TaskLogStatusDisplay,
} from '@/types/taskLog';
import { useAnalyticsParams } from '@/utils/urlParams';

const AUTO_REFRESH_INTERVAL_SECONDS = 5 * 60;

interface TaskLogTableRow {
  id: string;
  dateCreated: string;
  time: string;
  taskTitle: string;
  assignedBy: string;
  assignedTo: string;
  type: string;
  priority: TaskLogPriorityDisplay;
  status: TaskLogStatusDisplay;
  escalationPolicy: string;
  [key: string]:
    | string
    | TaskLogPriorityDisplay
    | TaskLogStatusDisplay
    | undefined;
}

const formatCountdown = (totalSeconds: number) => {
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

const TaskLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Get search term from URL params
  const searchParams = new URLSearchParams(location.search);
  const searchTerm = searchParams.get('search') || '';

  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 700);

  // Pagination state
  const { page: currentPage, setPage: setCurrentPage } = usePagination();
  const pageSize = 10;

  // Build filter using URL params and tenant - get filters from URL params
  const urlParams = new URLSearchParams(location.search);
  const statusFilter = (urlParams.get('status') as TaskLogStatus) || undefined;
  const priorityFilter =
    (urlParams.get('priority') as TaskLogPriority) || undefined;
  const fromDate = urlParams.get('from') || undefined;
  const toDate = urlParams.get('to') || undefined;

  // Memoize filter object to prevent infinite re-renders
  const taskLogFilter = useMemo(
    () => ({
      search: debouncedSearchTerm,
      createdBy: filters.agent || '',
      tenantId: tenantId || '',
      status: statusFilter,
      priority: priorityFilter,
      from: fromDate,
      to: toDate,
      page: currentPage,
      pageSize: pageSize,
    }),
    [
      debouncedSearchTerm,
      filters.agent,
      tenantId,
      statusFilter,
      priorityFilter,
      fromDate,
      toDate,
      currentPage,
      pageSize,
    ]
  );

  const queryEnabled =
    !!tenantId && !!taskLogFilter.createdBy && taskLogFilter.createdBy !== '';

  // Use the hook to fetch task logs with filter - only when we have both required fields
  const {
    data: taskLogsResponse,
    isLoading,
    isFetching,
    refetch,
  } = useTaskLogsList(taskLogFilter, queryEnabled);

  const [secondsUntilRefresh, setSecondsUntilRefresh] = useState(
    AUTO_REFRESH_INTERVAL_SECONDS
  );
  const [hasInitialLoadCompleted, setHasInitialLoadCompleted] = useState(false);
  const [pendingTimerReset, setPendingTimerReset] = useState(false);
  const autoRefreshInFlightRef = useRef(false);
  const wasFetchingRef = useRef(isFetching);

  useEffect(() => {
    if (!queryEnabled) {
      setHasInitialLoadCompleted(false);
      setSecondsUntilRefresh(AUTO_REFRESH_INTERVAL_SECONDS);
      setPendingTimerReset(false);
      autoRefreshInFlightRef.current = false;
      wasFetchingRef.current = false;
      return;
    }

    if (!hasInitialLoadCompleted && !isLoading && !isFetching) {
      setHasInitialLoadCompleted(true);
      setSecondsUntilRefresh(AUTO_REFRESH_INTERVAL_SECONDS);
    }
  }, [queryEnabled, hasInitialLoadCompleted, isLoading, isFetching]);

  useEffect(() => {
    if (!hasInitialLoadCompleted) {
      wasFetchingRef.current = isFetching;
      return;
    }

    if (isFetching && !wasFetchingRef.current) {
      setPendingTimerReset(true);
    }

    if (!isFetching && wasFetchingRef.current) {
      if (pendingTimerReset) {
        setSecondsUntilRefresh(AUTO_REFRESH_INTERVAL_SECONDS);
        setPendingTimerReset(false);
      }
      autoRefreshInFlightRef.current = false;
    }

    wasFetchingRef.current = isFetching;
  }, [hasInitialLoadCompleted, isFetching, pendingTimerReset]);

  useEffect(() => {
    if (!queryEnabled || !hasInitialLoadCompleted) {
      return;
    }

    const intervalId = setInterval(() => {
      setSecondsUntilRefresh(prevSeconds => {
        if (prevSeconds <= 1) {
          if (!autoRefreshInFlightRef.current && !isFetching) {
            autoRefreshInFlightRef.current = true;
            refetch();
          }
          return 0;
        }

        return prevSeconds - 1;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [queryEnabled, hasInitialLoadCompleted, refetch, isFetching]);

  const handleManualRefresh = () => {
    if (!queryEnabled || !hasInitialLoadCompleted || isFetching) {
      return;
    }

    refetch();
  };

  const taskLogs = Array.isArray(taskLogsResponse)
    ? taskLogsResponse
    : taskLogsResponse?.data?.tasks || [];
  const totalCount = taskLogsResponse?.data?.total || taskLogs.length;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Convert TaskLog data to table format
  const convertToTableData = (taskLogs: TaskLog[]): TaskLogTableRow[] => {
    return taskLogs.map(log => ({
      id: log.id,
      dateCreated: formatUserTimestamp(log.createdAt, 'date'),
      time: formatUserTimestamp(log.createdAt, 'time'),
      taskTitle: log.taskTitle,
      assignedTo: log.assignedTo || 'Unassigned',
      assignedBy: log.assignedBy || 'Unassigned',
      type: log?.taskTypeReference?.name || '--',
      priority: mapPriorityToDisplay(log.priority),
      status: mapStatusToDisplay(log.status),
      escalationPolicy: log.escalationPolicy || 'Default',
    }));
  };

  const tableData = convertToTableData(taskLogs);

  const allColumns = useMemo<Column<TaskLogTableRow>[]>(() => {
    const getStatusStyles = (status: string) => {
      switch (status) {
        case 'Completed':
          return 'bg-[#56965A] text-white';
        case 'Not Started':
          return 'bg-[#808894] text-white';
        case 'In Progress':
          return 'bg-[#F0A840] text-white';
        case 'Queued':
          return 'bg-[#3B82F6] text-white';
        case 'Failed':
          return 'bg-red-500 text-white';
        case 'Escalated':
          return 'bg-[#EA580C] text-white';
        default:
          return 'bg-gray-400 text-white';
      }
    };

    return [
      {
        key: 'dateCreated',
        label: 'timestamp',
        sortable: true,
        render: (value, row) => (
          <div className="flex flex-col items-start gap-1">
            <span>{value}</span>
            <span className="rounded border border-[#FFE0D1] px-1.5 py-1">
              {row.time}
            </span>
          </div>
        ),
      },
      {
        key: 'taskTitle',
        label: 'Task Title',
        sortable: true,
        wrap: isMobile,
        render: value => (
          <div
            className={
              isMobile
                ? 'line-clamp-2 max-w-24 text-sm leading-[18px] sm:max-w-xs'
                : 'max-w-xs truncate'
            }
            title={String(value)}
          >
            {String(value)}
          </div>
        ),
      },
      {
        key: 'assignedTo',
        label: 'Assigned To',
        sortable: true,
        render: value => (
          <div className="max-w-xs truncate capitalize" title={String(value)}>
            {String(value)}
          </div>
        ),
      },
      {
        key: 'type',
        label: 'Type',
        sortable: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        sortable: true,
        render: value => {
          const priority = value as TaskLogTableRow['priority'];
          return (
            <span className={`px-3 py-1.5 text-xs font-medium`}>
              {priority}
            </span>
          );
        },
      },
      {
        key: 'status',
        label: 'Status',
        sortable: true,
        render: value => {
          const status = value as TaskLogTableRow['status'];

          return (
            <span
              className={`rounded-md px-3 py-1.5 text-xs font-medium ${getStatusStyles(
                status
              )}`}
            >
              {status}
            </span>
          );
        },
      },
    ];
  }, [isMobile]);

  const columns: Column<TaskLogTableRow>[] = useMemo(() => {
    if (!isMobile) {
      return allColumns;
    }

    const mobileKeys: Array<keyof TaskLogTableRow> = [
      'dateCreated',
      'taskTitle',
      'status',
    ];

    return allColumns.filter(column => mobileKeys.includes(column.key));
  }, [allColumns, isMobile]);

  const handleRowClick = (row: TaskLogTableRow) => {
    // Navigate to task log details page
    navigate(ROUTES.DASHBOARD_ANALYTICS_TASK_LOG_DETAILS(row.id));
  };

  // Show empty state when no data and not loading
  if (!isLoading && tableData.length === 0) {
    return (
      <div className="px-4 sm:px-6">
        <EmptyState
          icon={
            searchTerm ? (
              <Icons.SearchX className="h-6 w-6 text-white sm:h-8 sm:w-8" />
            ) : (
              <Icons.FileEmpty className="h-6 w-6 text-white sm:h-8 sm:w-8" />
            )
          }
          title={
            searchTerm
              ? 'No task logs found matching your search'
              : 'Task Log is Empty.'
          }
          description={
            searchTerm
              ? "Try adjusting your search terms or filters to find what you're looking for."
              : 'As your agents begin to work, this log will track every action, message, and outcome, all in one place.'
          }
        />
      </div>
    );
  }

  return (
    <>
      <TablePageContainer>
        <div className="mb-6 flex flex-row items-center justify-center gap-5 text-sm text-[#6B7280]">
          <div className="flex items-center gap-2 text-[#383F45]">
            <Icons.RefreshAuto className="h-4 w-4 text-disabled" />
            <span className="text-sm font-medium text-disabled">
              Auto-Refresh:
            </span>
            <span className="text-base font-bold text-[#1A1A1A]">
              {hasInitialLoadCompleted
                ? formatCountdown(secondsUntilRefresh)
                : '--:--'}
            </span>
          </div>
          <button
            type="button"
            onClick={handleManualRefresh}
            disabled={!hasInitialLoadCompleted || isFetching}
            className="w-full rounded-lg border border-primary bg-[#FFF0EB] px-4 py-2 text-sm font-semibold text-primary transition hover:bg-primary/10 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto"
          >
            {isFetching ? 'Refreshing…' : 'Refresh Now'}
          </button>
        </div>

        <DataTable
          data={tableData}
          columns={columns}
          onRowClick={handleRowClick}
          loading={isLoading}
          emptyMessage={
            searchTerm
              ? 'No task logs found matching your search'
              : 'No task logs found'
          }
          rowColoring={true}
          rowColoringType="odd"
          showCheckAll={false}
        />
      </TablePageContainer>

      {/* Pagination */}
      {totalPages > 0 && !isLoading && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </>
  );
};

export default TaskLogsPage;
