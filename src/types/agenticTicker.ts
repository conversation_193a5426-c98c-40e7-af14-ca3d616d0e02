export interface AgenticTickerEntry {
  id: string;
  eventId: string;
  eventType: 'Message' | 'Task' | 'Follow-Up';
  owner: {
    name: string;
    image: string;
  };
  receiver: {
    name: string;
    image?: string; // Optional for external clients
    isExternal: boolean;
  };
  timestamp: Date;
  status: 'NOT STARTED' | 'IN PROGRESS' | 'COMPLETED' | 'BLOCKED';
}

export interface FilterState {
  suites: string[];
  agents: string[];
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
    quickSelect:
      | 'Today'
      | 'This Week'
      | 'This Month'
      | 'This Year'
      | 'Custom'
      | null;
  };
  eventTypes: ('Message' | 'Task' | 'Follow-Up')[];
  statuses: ('NOT STARTED' | 'IN PROGRESS' | 'COMPLETED' | 'BLOCKED')[];
  searchQuery: string;
}

export interface FilterTag {
  id: string;
  type: 'suite' | 'agent' | 'date' | 'eventType' | 'status';
  label: string;
  value: string;
}

export interface AgenticTickerPageState {
  entries: AgenticTickerEntry[];
  filteredEntries: AgenticTickerEntry[];
  filters: FilterState;
  filterTags: FilterTag[];
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  emptyStateType: 'no-data' | 'no-results' | 'api-error' | null;
}

export interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  quickSelect: string | null;
  onDateChange: (startDate: Date | null, endDate: Date | null) => void;
  onQuickSelectChange: (option: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export interface MultiSelectDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  selectedItems: string[];
  options: { value: string; label: string; icon?: string }[];
  onItemToggle: (value: string) => void;
  placeholder: string;
  title: string;
}

export interface StatusPillProps {
  status: 'NOT STARTED' | 'IN PROGRESS' | 'COMPLETED' | 'BLOCKED';
  className?: string;
}

export interface OwnerReceiverModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'owner' | 'receiver';
  data: {
    name: string;
    image?: string;
    isExternal?: boolean;
  };
}
